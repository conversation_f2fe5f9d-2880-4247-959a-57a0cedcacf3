package activity_cashback

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestTaskCompletionLogic tests the basic logic for task completion validation
func TestTaskCompletionLogic(t *testing.T) {
	t.Run("Daily task frequency validation", func(t *testing.T) {
		// Test that daily tasks have the correct frequency
		task := &model.ActivityTask{
			Frequency: model.FrequencyDaily,
		}
		
		assert.Equal(t, model.FrequencyDaily, task.Frequency)
		assert.NotEqual(t, model.FrequencyOneTime, task.Frequency)
	})

	t.Run("Time truncation logic for daily comparison", func(t *testing.T) {
		// Test that time truncation works correctly for daily comparison
		now := time.Now()
		today := now.Truncate(24 * time.Hour)
		
		// Same day should be equal after truncation
		sameDay := now.Add(2 * time.Hour)
		sameDayTruncated := sameDay.Truncate(24 * time.Hour)
		assert.True(t, today.Equal(sameDayTruncated))
		
		// Different day should not be equal after truncation
		yesterday := now.AddDate(0, 0, -1)
		yesterdayTruncated := yesterday.Truncate(24 * time.Hour)
		assert.False(t, today.Equal(yesterdayTruncated))
	})

	t.Run("Category name validation", func(t *testing.T) {
		// Test category constants
		assert.Equal(t, "daily", string(model.CategoryDaily))
		assert.Equal(t, "community", string(model.CategoryCommunity))
		assert.Equal(t, "trading", string(model.CategoryTrading))
	})

	t.Run("Task frequency constants", func(t *testing.T) {
		// Test frequency constants
		assert.Equal(t, "DAILY", string(model.FrequencyDaily))
		assert.Equal(t, "ONE_TIME", string(model.FrequencyOneTime))
		assert.Equal(t, "UNLIMITED", string(model.FrequencyUnlimited))
	})
}

// TestTaskCategoryLogic tests task category related logic
func TestTaskCategoryLogic(t *testing.T) {
	t.Run("Task with daily category and daily frequency", func(t *testing.T) {
		task := &model.ActivityTask{
			Frequency: model.FrequencyDaily,
			Category: model.TaskCategory{
				Name: model.CategoryDaily,
			},
		}
		
		// Both frequency and category should be daily
		assert.Equal(t, model.FrequencyDaily, task.Frequency)
		assert.Equal(t, model.CategoryDaily, task.Category.Name)
	})

	t.Run("Task with community category and daily frequency", func(t *testing.T) {
		task := &model.ActivityTask{
			Frequency: model.FrequencyDaily,
			Category: model.TaskCategory{
				Name: model.CategoryCommunity,
			},
		}
		
		// Frequency should be daily, category should be community
		assert.Equal(t, model.FrequencyDaily, task.Frequency)
		assert.Equal(t, model.CategoryCommunity, task.Category.Name)
	})

	t.Run("Task with trading category and daily frequency", func(t *testing.T) {
		task := &model.ActivityTask{
			Frequency: model.FrequencyDaily,
			Category: model.TaskCategory{
				Name: model.CategoryTrading,
			},
		}
		
		// Frequency should be daily, category should be trading
		assert.Equal(t, model.FrequencyDaily, task.Frequency)
		assert.Equal(t, model.CategoryTrading, task.Category.Name)
	})
}

// TestUserTaskProgressLogic tests user task progress related logic
func TestUserTaskProgressLogic(t *testing.T) {
	t.Run("Progress with completion time today", func(t *testing.T) {
		now := time.Now()
		progress := &model.UserTaskProgress{
			LastCompletedAt: &now,
		}
		
		// Should have completion time
		assert.NotNil(t, progress.LastCompletedAt)
		
		// Check if completed today
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		assert.True(t, today.Equal(lastCompleted))
	})

	t.Run("Progress with completion time yesterday", func(t *testing.T) {
		yesterday := time.Now().AddDate(0, 0, -1)
		progress := &model.UserTaskProgress{
			LastCompletedAt: &yesterday,
		}
		
		// Should have completion time
		assert.NotNil(t, progress.LastCompletedAt)
		
		// Check if NOT completed today
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		assert.False(t, today.Equal(lastCompleted))
	})

	t.Run("Progress without completion time", func(t *testing.T) {
		progress := &model.UserTaskProgress{
			LastCompletedAt: nil,
		}
		
		// Should not have completion time
		assert.Nil(t, progress.LastCompletedAt)
	})
}
